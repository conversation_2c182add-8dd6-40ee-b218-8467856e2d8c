@startuml Example System Architecture

!theme plain
title Healthcare System Architecture Example

' Define actors
actor Patient as P
actor Doctor as D
actor <PERSON><PERSON> as A

' Define systems
package "Frontend" {
  [Web Portal] as WP
  [Mobile App] as MA
}

package "Backend Services" {
  [Authentication Service] as AUTH
  [Patient Service] as PS
  [Appointment Service] as AS
  [Notification Service] as NS
}

package "Data Layer" {
  database "Patient DB" as PDB
  database "Appointment DB" as ADB
  database "User DB" as UDB
}

package "External Services" {
  [Email Service] as EMAIL
  [SMS Service] as SMS
  [Payment Gateway] as PAY
}

' Define relationships
P --> WP : Uses
P --> MA : Uses
D --> WP : Uses
A --> WP : Uses

WP --> AUTH : Authenticates
MA --> AUTH : Authenticates

WP --> PS : Patient Data
WP --> AS : Appointments
MA --> PS : Patient Data
MA --> AS : Appointments

AUTH --> UDB : User Credentials
PS --> PDB : Patient Records
AS --> ADB : Appointment Data

AS --> NS : Send Notifications
NS --> EMAIL : Email Alerts
NS --> SMS : SMS Alerts

AS --> PAY : Process Payments

' Add notes
note right of PS : Handles FHIR\nCompliant Data
note bottom of AUTH : JWT Token\nBased Auth
note left of NS : Real-time\nNotifications

@enduml
