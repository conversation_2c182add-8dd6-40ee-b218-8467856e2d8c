@startuml FHIR Bundle Database Schema
!theme plain
skinparam linetype polyline
skinparam nodesep 10
skinparam ranksep 20

' Main Bundle entity
entity "Bundle" as bundle {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  type : VARCHAR(20)
  timestamp : DATETIME
  meta_profile : TEXT
}

' Composition entity
entity "Composition" as composition {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  status : VARCHAR(20)
  date : DATETIME
  title : VARCHAR(100)
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(50)
  text_status : VARCHAR(20)
  meta_profile : TEXT
  subject_reference : VARCHAR(100) <<FK>>
  encounter_reference : VARCHAR(100) <<FK>>
  custodian_reference : VARCHAR(100) <<FK>>
}

' Patient entity
entity "Patient" as patient {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHA<PERSON>(20)
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(20)
  name_text : VARCHAR(100)
  address_use : VARCHAR(20)
  address_line : VARCHAR(255)
  address_city : VARCHAR(50)
  address_postalCode : VARCHAR(10)
  text_status : VARCHAR(20)
  meta_profile : TEXT
}

' Specimen entity
entity "Specimen" as specimen {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  accessionIdentifier_system : VARCHAR(255)
  accessionIdentifier_value : VARCHAR(50)
  collection_collectedDateTime : DATETIME
  text_status : VARCHAR(20)
  meta_profile : TEXT
  subject_reference : VARCHAR(100) <<FK>>
}

' ServiceRequest entity
entity "ServiceRequest" as servicerequest {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  status : VARCHAR(20)
  intent : VARCHAR(20)
  authoredOn : DATETIME
  code_text : TEXT
  text_status : VARCHAR(20)
  meta_profile : TEXT
  subject_reference : VARCHAR(100) <<FK>>
  requester_reference : VARCHAR(100) <<FK>>
}

' Encounter entity
entity "Encounter" as encounter {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  status : VARCHAR(20)
  class_system : VARCHAR(255)
  class_code : VARCHAR(10)
  class_display : VARCHAR(100)
  period_start : DATETIME
  text_status : VARCHAR(20)
  meta_profile : TEXT
  subject_reference : VARCHAR(100) <<FK>>
}

' DiagnosticReport entity
entity "DiagnosticReport" as diagnosticreport {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(50)
  status : VARCHAR(20)
  effectiveDateTime : DATETIME
  text_status : VARCHAR(20)
  meta_profile : TEXT
  specimen_reference : VARCHAR(100) <<FK>>
  performer_reference : VARCHAR(100) <<FK>>
}

' Observation entity
entity "Observation" as observation {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  status : VARCHAR(20)
  effectiveDateTime : DATETIME
  text_status : VARCHAR(20)
  meta_profile : TEXT
}

' Organization entity (Lab Provider)
entity "Organization_Lab" as org_lab {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(50)
  name : VARCHAR(255)
  type_code : VARCHAR(10)
  type_display : VARCHAR(100)
  text_status : VARCHAR(20)
  meta_profile : TEXT
}

' Organization entity (Requester)
entity "Organization_Requester" as org_req {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(50)
  name : VARCHAR(255)
  type_code : VARCHAR(10)
  type_display : VARCHAR(100)
  text_status : VARCHAR(20)
  meta_profile : TEXT
}

' PractitionerRole entity
entity "PractitionerRole" as practitionerrole {
  * id : VARCHAR(36) <<PK>>
  --
  resourceType : VARCHAR(20)
  active : BOOLEAN
  practitioner_identifier_system : VARCHAR(255)
  practitioner_identifier_value : VARCHAR(50)
  practitioner_display : VARCHAR(100)
  organization_identifier_system : VARCHAR(255)
  organization_identifier_value : VARCHAR(50)
  organization_display : VARCHAR(255)
  text_status : VARCHAR(20)
  meta_profile : TEXT
}

' Extension tables for complex nested data
entity "Composition_Extension" as comp_ext {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  url : VARCHAR(255)
  valueReference_reference : VARCHAR(100)
  valueReference_type : VARCHAR(50)
}

entity "Composition_Type_Coding" as comp_type {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "Composition_Category_Coding" as comp_cat {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "Composition_Author" as comp_author {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  reference : VARCHAR(100)
  type : VARCHAR(50)
  display : VARCHAR(255)
}

entity "Composition_Attester" as comp_attester {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  mode : VARCHAR(20)
  time : DATETIME
  party_reference : VARCHAR(100)
  party_display : VARCHAR(255)
}

entity "Composition_Section" as comp_section {
  * id : INT <<PK>>
  --
  composition_id : VARCHAR(36) <<FK>>
  code_system : VARCHAR(255)
  entry_reference : VARCHAR(100)
}

entity "Patient_Extension" as pat_ext {
  * id : INT <<PK>>
  --
  patient_id : VARCHAR(36) <<FK>>
  url : VARCHAR(255)
  extension_url : VARCHAR(255)
  valueCodeableConcept_system : VARCHAR(255)
  valueCodeableConcept_code : VARCHAR(10)
  valueCodeableConcept_display : VARCHAR(100)
}

entity "Specimen_Container" as spec_container {
  * id : INT <<PK>>
  --
  specimen_id : VARCHAR(36) <<FK>>
  identifier_system : VARCHAR(255)
  identifier_value : VARCHAR(50)
}

entity "ServiceRequest_Extension" as sr_ext {
  * id : INT <<PK>>
  --
  servicerequest_id : VARCHAR(36) <<FK>>
  url : VARCHAR(255)
  valueDateTime : DATETIME
}

entity "ServiceRequest_Identifier" as sr_id {
  * id : INT <<PK>>
  --
  servicerequest_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  value : VARCHAR(50)
}

entity "ServiceRequest_Category_Coding" as sr_cat {
  * id : INT <<PK>>
  --
  servicerequest_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
}

entity "ServiceRequest_Code_Coding" as sr_code {
  * id : INT <<PK>>
  --
  servicerequest_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "Encounter_BasedOn" as enc_basedon {
  * id : INT <<PK>>
  --
  encounter_id : VARCHAR(36) <<FK>>
  reference : VARCHAR(100)
  type : VARCHAR(50)
}

entity "Encounter_Participant" as enc_participant {
  * id : INT <<PK>>
  --
  encounter_id : VARCHAR(36) <<FK>>
  type_system : VARCHAR(255)
  type_code : VARCHAR(10)
  type_display : VARCHAR(100)
  individual_reference : VARCHAR(100)
  individual_type : VARCHAR(50)
}

entity "DiagnosticReport_Category_Coding" as dr_cat {
  * id : INT <<PK>>
  --
  diagnosticreport_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "DiagnosticReport_Code_Coding" as dr_code {
  * id : INT <<PK>>
  --
  diagnosticreport_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "DiagnosticReport_PresentedForm" as dr_form {
  * id : INT <<PK>>
  --
  diagnosticreport_id : VARCHAR(36) <<FK>>
  contentType : VARCHAR(50)
  data : LONGTEXT
  title : VARCHAR(255)
}

entity "Observation_Category_Coding" as obs_cat {
  * id : INT <<PK>>
  --
  observation_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
}

entity "Observation_Code_Coding" as obs_code {
  * id : INT <<PK>>
  --
  observation_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
}

entity "Observation_Method_Coding" as obs_method {
  * id : INT <<PK>>
  --
  observation_id : VARCHAR(36) <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
}

entity "Observation_Component" as obs_comp {
  * id : INT <<PK>>
  --
  observation_id : VARCHAR(36) <<FK>>
  code_system : VARCHAR(255)
  code_code : VARCHAR(50)
  code_display : VARCHAR(255)
  code_text : VARCHAR(255)
  valueString : TEXT
}

entity "Observation_Component_ValueCoding" as obs_comp_val {
  * id : INT <<PK>>
  --
  component_id : INT <<FK>>
  system : VARCHAR(255)
  code : VARCHAR(50)
  display : VARCHAR(255)
  text : VARCHAR(255)
}

' Bundle Entry relationship table
entity "Bundle_Entry" as bundle_entry {
  * id : INT <<PK>>
  --
  bundle_id : VARCHAR(36) <<FK>>
  fullUrl : VARCHAR(255)
  resource_type : VARCHAR(50)
  resource_id : VARCHAR(36)
  request_method : VARCHAR(10)
  request_url : VARCHAR(255)
}

' Relationships
bundle ||--o{ bundle_entry : "contains"

' Main resource relationships
composition }o--|| patient : "subject"
composition }o--|| encounter : "encounter"
composition }o--|| org_lab : "custodian"

specimen }o--|| patient : "subject"

servicerequest }o--|| patient : "subject"
servicerequest }o--|| org_req : "requester"

encounter }o--|| patient : "subject"

diagnosticreport }o--|| specimen : "specimen"
diagnosticreport }o--|| org_lab : "performer"

' Extension relationships
composition ||--o{ comp_ext : "has extensions"
composition ||--o{ comp_type : "has type codings"
composition ||--o{ comp_cat : "has category codings"
composition ||--o{ comp_author : "has authors"
composition ||--o{ comp_attester : "has attesters"
composition ||--o{ comp_section : "has sections"

patient ||--o{ pat_ext : "has extensions"

specimen ||--o{ spec_container : "has containers"

servicerequest ||--o{ sr_ext : "has extensions"
servicerequest ||--o{ sr_id : "has identifiers"
servicerequest ||--o{ sr_cat : "has category codings"
servicerequest ||--o{ sr_code : "has code codings"

encounter ||--o{ enc_basedon : "based on"
encounter ||--o{ enc_participant : "has participants"

diagnosticreport ||--o{ dr_cat : "has category codings"
diagnosticreport ||--o{ dr_code : "has code codings"
diagnosticreport ||--o{ dr_form : "has presented forms"

observation ||--o{ obs_cat : "has category codings"
observation ||--o{ obs_code : "has code codings"
observation ||--o{ obs_method : "has method codings"
observation ||--o{ obs_comp : "has components"

obs_comp ||--o{ obs_comp_val : "has value codings"

@enduml
