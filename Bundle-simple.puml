@startuml FHIR Bundle Simple Schema
!theme plain

class Bundle {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +type: VARCHAR(20)
  +timestamp: DATETIME
  +meta_profile: TEXT
}

class Composition {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +status: VARCHAR(20)
  +date: DATETIME
  +title: VARCHAR(100)
  +identifier_system: VARCHAR(255)
  +identifier_value: VARCHAR(50)
  +subject_reference: VARCHAR(100)
  +encounter_reference: VARCHAR(100)
  +custodian_reference: VARCHAR(100)
}

class Patient {
  +id: VARCHAR(36)
  +resourceType: VA<PERSON>HAR(20)
  +identifier_system: VARCHAR(255)
  +identifier_value: VARCHAR(20)
  +name_text: VARCHAR(100)
  +address_use: VARCHAR(20)
  +address_line: VARCHAR(255)
  +address_city: VARCHAR(50)
  +address_postalCode: VARCHAR(10)
}

class Specimen {
  +id: VARCHA<PERSON>(36)
  +resourceType: VARCHAR(20)
  +accessionIdentifier_system: VARCHAR(255)
  +accessionIdentifier_value: VA<PERSON>HAR(50)
  +collection_collectedDateTime: DATETIME
  +subject_reference: VARCHAR(100)
}

class ServiceRequest {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +status: VARCHAR(20)
  +intent: VARCHAR(20)
  +authoredOn: DATETIME
  +code_text: TEXT
  +subject_reference: VARCHAR(100)
  +requester_reference: VARCHAR(100)
}

class Encounter {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +status: VARCHAR(20)
  +class_system: VARCHAR(255)
  +class_code: VARCHAR(10)
  +class_display: VARCHAR(100)
  +period_start: DATETIME
  +subject_reference: VARCHAR(100)
}

class DiagnosticReport {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +identifier_system: VARCHAR(255)
  +identifier_value: VARCHAR(50)
  +status: VARCHAR(20)
  +effectiveDateTime: DATETIME
  +specimen_reference: VARCHAR(100)
  +performer_reference: VARCHAR(100)
}

class Observation {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +status: VARCHAR(20)
  +effectiveDateTime: DATETIME
}

class Organization_Lab {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +identifier_system: VARCHAR(255)
  +identifier_value: VARCHAR(50)
  +name: VARCHAR(255)
  +type_code: VARCHAR(10)
  +type_display: VARCHAR(100)
}

class Organization_Requester {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +identifier_system: VARCHAR(255)
  +identifier_value: VARCHAR(50)
  +name: VARCHAR(255)
  +type_code: VARCHAR(10)
  +type_display: VARCHAR(100)
}

class PractitionerRole {
  +id: VARCHAR(36)
  +resourceType: VARCHAR(20)
  +active: BOOLEAN
  +practitioner_identifier_system: VARCHAR(255)
  +practitioner_identifier_value: VARCHAR(50)
  +practitioner_display: VARCHAR(100)
  +organization_identifier_system: VARCHAR(255)
  +organization_identifier_value: VARCHAR(50)
  +organization_display: VARCHAR(255)
}

' Relationships
Bundle ||--o{ Composition : contains
Bundle ||--o{ Patient : contains
Bundle ||--o{ Specimen : contains
Bundle ||--o{ ServiceRequest : contains
Bundle ||--o{ Encounter : contains
Bundle ||--o{ DiagnosticReport : contains
Bundle ||--o{ Observation : contains
Bundle ||--o{ Organization_Lab : contains
Bundle ||--o{ Organization_Requester : contains
Bundle ||--o{ PractitionerRole : contains

Composition }o--|| Patient : subject
Composition }o--|| Encounter : encounter
Composition }o--|| Organization_Lab : custodian

Specimen }o--|| Patient : subject

ServiceRequest }o--|| Patient : subject
ServiceRequest }o--|| Organization_Requester : requester

Encounter }o--|| Patient : subject

DiagnosticReport }o--|| Specimen : specimen
DiagnosticReport }o--|| Organization_Lab : performer

note top of Bundle : "Main FHIR Bundle containing\nall laboratory report resources"
note right of Patient : "Patient: HASSAN, INT-MOHAMMAD\nPersonnummer: 19350416-7358"
note right of Organization_Lab : "Patologen Lund, som lab\nLUPALB"
note right of Organization_Requester : "Barn- och Ungdomsmottagning Ystad\nYSBAUM"

@enduml
