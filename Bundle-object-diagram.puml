@startuml FHIR Bundle Object Diagram
!theme plain
skinparam object {
  BackgroundColor lightblue
  BorderColor black
}
skinparam note {
  BackgroundColor lightyellow
}

object "Bundle" as bundle {
  id = "377bf7b8-6d6f-3be5-799c-df2fec9e08f6"
  resourceType = "Bundle"
  type = "transaction"
  timestamp = "2021-03-09T10:52:00+01:00"
  profile = "Bundle-eu-lab"
}

object "Composition" as comp {
  id = "56eb85e8-d636-bae6-2602-3b328a3676d2"
  resourceType = "Composition"
  status = "final"
  title = "Laboratorierapport"
  date = "2021-03-09T10:52:00+01:00"
  identifier = "21PL00067"
}

object "Patient" as patient {
  id = "27de3e8b-0f21-40b6-0c8a-10e321c0e124"
  resourceType = "Patient"
  sex = "Mann"
  profile = "Patient-eu-lab"
}

object "Encounter" as encounter {
  id = "0e4e49f3-4d4b-69bf-0477-7068a9b9b41d"
  resourceType = "Encounter"
  status = "unknown"
  class = "AMB"
  start = "2021-03-09T10:34:00+01:00"
}

object "DiagnosticReport" as report {
  id = "7475320a-caf7-4f31-4d71-cb41ce90d048"
  resourceType = "DiagnosticReport"
  status = "registered"
  effectiveDateTime = "2021-03-09T10:47:00+01:00"
}

object "Observation" as obs {
  id = "81c53f8d-7c94-0a24-f931-6dcd9119a811"
  resourceType = "Observation"
  status = "final"
  category = "laboratory"
  code = "MI - Mikroskopisk undersøkelse"
  valueString = "Utlåtande"
}

object "ServiceRequest" as service {
  id = "7053f446-2f08-0594-95a5-ae20ead22bbf"
  resourceType = "ServiceRequest"
  status = "unknown"
  intent = "order"
}

object "Organization_Lab" as orgLab {
  id = "fd35dd09-eecb-e824-cb72-debf681b931f"
  resourceType = "Organization"
  name = "Patologen Lund, som lab"
  identifier = "LUPALB"
}

object "PractitionerRole" as practRole {
  id = "bf1f2368-18c5-dcd8-3bf0-a8a4d3ff8465"
  resourceType = "PractitionerRole"
  active = true
  organization = "YSBAUM"
  display = "Barn- och Ungdomsmottagning Ystad"
}

' Bundle contains all resources
bundle ||--o{ comp : contains
bundle ||--o{ patient : contains
bundle ||--o{ encounter : contains
bundle ||--o{ report : contains
bundle ||--o{ obs : contains
bundle ||--o{ service : contains
bundle ||--o{ orgLab : contains
bundle ||--o{ practRole : contains

' Resource relationships
comp --> patient : subject
comp --> encounter : encounter
comp --> orgLab : author/custodian
comp --> report : diagnosticReportReference
comp --> service : basedOn
comp --> obs : section.entry

encounter --> patient : subject
encounter --> practRole : participant

report --> patient : subject
report --> orgLab : performer

obs --> patient : subject

service --> patient : subject

note top of bundle : "Laboratory Report Bundle\nPathology examination\nMarch 9, 2021"

note right of obs : "Microscopic examination\nwith statement/opinion"

note bottom of orgLab : "Performing laboratory:\nPathology Lund"

@enduml
