@startuml FHIR Patient Management Classes

!theme plain
title FHIR Patient Management System - Class Diagram

abstract class Resource {
  +id: String
  +meta: Meta
  +implicitRules: String
  +language: String
  +text: Narrative
  +contained: Resource[]
  +extension: Extension[]
  +modifierExtension: Extension[]
  --
  +validate(): Boolean
  +toJson(): String
  +fromJson(json: String): Resource
}

class Patient {
  +identifier: Identifier[]
  +active: Boolean
  +name: HumanName[]
  +telecom: ContactPoint[]
  +gender: String
  +birthDate: Date
  +address: Address[]
  +maritalStatus: CodeableConcept
  +contact: PatientContact[]
  +communication: PatientCommunication[]
  +generalPractitioner: Reference[]
  +managingOrganization: Reference
  --
  +getFullName(): String
  +getAge(): Integer
  +getPrimaryContact(): ContactPoint
  +addIdentifier(identifier: Identifier): void
  +updateAddress(address: Address): void
}

class Practitioner {
  +identifier: Identifier[]
  +active: Boolean
  +name: <PERSON><PERSON>ame[]
  +telecom: ContactPoint[]
  +address: Address[]
  +gender: String
  +birthDate: Date
  +photo: Attachment[]
  +qualification: PractitionerQualification[]
  +communication: CodeableConcept[]
  --
  +getSpecialties(): String[]
  +isActive(): Boolean
  +addQualification(qualification: PractitionerQualification): void
}

class Appointment {
  +identifier: Identifier[]
  +status: String
  +serviceCategory: CodeableConcept[]
  +serviceType: CodeableConcept[]
  +specialty: CodeableConcept[]
  +appointmentType: CodeableConcept
  +reasonCode: CodeableConcept[]
  +priority: Integer
  +description: String
  +start: DateTime
  +end: DateTime
  +minutesDuration: Integer
  +slot: Reference[]
  +created: DateTime
  +comment: String
  +participant: AppointmentParticipant[]
  --
  +getDuration(): Integer
  +isUpcoming(): Boolean
  +canCancel(): Boolean
  +addParticipant(participant: AppointmentParticipant): void
  +reschedule(newStart: DateTime, newEnd: DateTime): void
}

class Organization {
  +identifier: Identifier[]
  +active: Boolean
  +type: CodeableConcept[]
  +name: String
  +alias: String[]
  +telecom: ContactPoint[]
  +address: Address[]
  +partOf: Reference
  +contact: OrganizationContact[]
  +endpoint: Reference[]
  --
  +getPrimaryContact(): ContactPoint
  +getMainAddress(): Address
  +isActive(): Boolean
}

class AppointmentParticipant {
  +type: CodeableConcept[]
  +actor: Reference
  +required: String
  +status: String
  +period: Period
  --
  +isRequired(): Boolean
  +getParticipantName(): String
}

' Relationships
Resource <|-- Patient
Resource <|-- Practitioner
Resource <|-- Appointment
Resource <|-- Organization

Patient ||--o{ Appointment : "has appointments"
Practitioner ||--o{ Appointment : "provides care"
Organization ||--o{ Patient : "manages"
Organization ||--o{ Practitioner : "employs"
Appointment ||--o{ AppointmentParticipant : "includes"

' Notes
note right of Patient : Represents a person\nreceiving healthcare\nservices
note right of Practitioner : Healthcare provider\n(doctor, nurse, etc.)
note bottom of Appointment : Scheduled healthcare\nencounter between\npatient and practitioner

@enduml
