@startuml Patient Appointment Booking Sequence

!theme plain
title Patient Appointment Booking Flow

actor Patient as P
participant "Web Portal" as WP
participant "Auth Service" as AUTH
participant "Appointment Service" as AS
participant "Patient Service" as PS
participant "Notification Service" as NS
database "Database" as DB

== Authentication ==
P -> WP: Login Request
WP -> AUTH: Validate Credentials
AUTH -> DB: Check User
DB --> AUTH: User Data
AUTH --> WP: JWT Token
WP --> P: Login Success

== Appointment Booking ==
P -> WP: Request Available Slots
WP -> AS: Get Available Appointments
AS -> DB: Query Available Slots
DB --> AS: Available Times
AS --> WP: Available Slots
WP --> P: Display Available Times

P -> WP: Select Appointment Slot
WP -> AS: Book Appointment
AS -> PS: Validate Patient
PS -> DB: Check Patient Record
DB --> PS: Patient Valid
PS --> AS: Patient Confirmed

AS -> DB: Create Appointment
DB --> AS: Appointment Created
AS -> NS: Send Confirmation
NS -> P: Email/SMS Confirmation
AS --> WP: Booking Confirmed
WP --> P: Success Message

== Error Handling ==
alt Appointment Slot Taken
    AS -> DB: Create Appointment
    DB --> AS: Slot Already Booked
    AS --> WP: Booking Failed
    WP --> P: Error: Slot Unavailable
else Patient Not Found
    PS -> DB: Check Patient Record
    DB --> PS: Patient Not Found
    PS --> AS: Patient Invalid
    AS --> WP: Booking Failed
    WP --> P: Error: Patient Not Found
end

@enduml
